{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/personal%20projects/CREATEXP/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/personal%20projects/CREATEXP/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/personal%20projects/CREATEXP/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/personal%20projects/CREATEXP/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/personal%20projects/CREATEXP/src/components/ui/calendar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport {\n  ChevronDownIcon,\n  ChevronLeftIcon,\n  ChevronRightIcon,\n} from \"lucide-react\"\nimport { DayButton, DayPicker, getDefaultClassNames } from \"react-day-picker\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Button, buttonVariants } from \"@/components/ui/button\"\n\nfunction Calendar({\n  className,\n  classNames,\n  showOutsideDays = true,\n  captionLayout = \"label\",\n  buttonVariant = \"ghost\",\n  formatters,\n  components,\n  ...props\n}: React.ComponentProps<typeof DayPicker> & {\n  buttonVariant?: React.ComponentProps<typeof Button>[\"variant\"]\n}) {\n  const defaultClassNames = getDefaultClassNames()\n\n  return (\n    <DayPicker\n      showOutsideDays={showOutsideDays}\n      className={cn(\n        \"bg-background group/calendar p-3 [--cell-size:--spacing(8)] [[data-slot=card-content]_&]:bg-transparent [[data-slot=popover-content]_&]:bg-transparent\",\n        String.raw`rtl:**:[.rdp-button\\_next>svg]:rotate-180`,\n        String.raw`rtl:**:[.rdp-button\\_previous>svg]:rotate-180`,\n        className\n      )}\n      captionLayout={captionLayout}\n      formatters={{\n        formatMonthDropdown: (date) =>\n          date.toLocaleString(\"default\", { month: \"short\" }),\n        ...formatters,\n      }}\n      classNames={{\n        root: cn(\"w-fit\", defaultClassNames.root),\n        months: cn(\n          \"flex gap-4 flex-col md:flex-row relative\",\n          defaultClassNames.months\n        ),\n        month: cn(\"flex flex-col w-full gap-4\", defaultClassNames.month),\n        nav: cn(\n          \"flex items-center gap-1 w-full absolute top-0 inset-x-0 justify-between\",\n          defaultClassNames.nav\n        ),\n        button_previous: cn(\n          buttonVariants({ variant: buttonVariant }),\n          \"size-(--cell-size) aria-disabled:opacity-50 p-0 select-none\",\n          defaultClassNames.button_previous\n        ),\n        button_next: cn(\n          buttonVariants({ variant: buttonVariant }),\n          \"size-(--cell-size) aria-disabled:opacity-50 p-0 select-none\",\n          defaultClassNames.button_next\n        ),\n        month_caption: cn(\n          \"flex items-center justify-center h-(--cell-size) w-full px-(--cell-size)\",\n          defaultClassNames.month_caption\n        ),\n        dropdowns: cn(\n          \"w-full flex items-center text-sm font-medium justify-center h-(--cell-size) gap-1.5\",\n          defaultClassNames.dropdowns\n        ),\n        dropdown_root: cn(\n          \"relative has-focus:border-ring border border-input shadow-xs has-focus:ring-ring/50 has-focus:ring-[3px] rounded-md\",\n          defaultClassNames.dropdown_root\n        ),\n        dropdown: cn(\n          \"absolute bg-popover inset-0 opacity-0\",\n          defaultClassNames.dropdown\n        ),\n        caption_label: cn(\n          \"select-none font-medium\",\n          captionLayout === \"label\"\n            ? \"text-sm\"\n            : \"rounded-md pl-2 pr-1 flex items-center gap-1 text-sm h-8 [&>svg]:text-muted-foreground [&>svg]:size-3.5\",\n          defaultClassNames.caption_label\n        ),\n        table: \"w-full border-collapse\",\n        weekdays: cn(\"flex\", defaultClassNames.weekdays),\n        weekday: cn(\n          \"text-muted-foreground rounded-md flex-1 font-normal text-[0.8rem] select-none\",\n          defaultClassNames.weekday\n        ),\n        week: cn(\"flex w-full mt-2\", defaultClassNames.week),\n        week_number_header: cn(\n          \"select-none w-(--cell-size)\",\n          defaultClassNames.week_number_header\n        ),\n        week_number: cn(\n          \"text-[0.8rem] select-none text-muted-foreground\",\n          defaultClassNames.week_number\n        ),\n        day: cn(\n          \"relative w-full h-full p-0 text-center [&:first-child[data-selected=true]_button]:rounded-l-md [&:last-child[data-selected=true]_button]:rounded-r-md group/day aspect-square select-none\",\n          defaultClassNames.day\n        ),\n        range_start: cn(\n          \"rounded-l-md bg-accent\",\n          defaultClassNames.range_start\n        ),\n        range_middle: cn(\"rounded-none\", defaultClassNames.range_middle),\n        range_end: cn(\"rounded-r-md bg-accent\", defaultClassNames.range_end),\n        today: cn(\n          \"bg-accent text-accent-foreground rounded-md data-[selected=true]:rounded-none\",\n          defaultClassNames.today\n        ),\n        outside: cn(\n          \"text-muted-foreground aria-selected:text-muted-foreground\",\n          defaultClassNames.outside\n        ),\n        disabled: cn(\n          \"text-muted-foreground opacity-50\",\n          defaultClassNames.disabled\n        ),\n        hidden: cn(\"invisible\", defaultClassNames.hidden),\n        ...classNames,\n      }}\n      components={{\n        Root: ({ className, rootRef, ...props }) => {\n          return (\n            <div\n              data-slot=\"calendar\"\n              ref={rootRef}\n              className={cn(className)}\n              {...props}\n            />\n          )\n        },\n        Chevron: ({ className, orientation, ...props }) => {\n          if (orientation === \"left\") {\n            return (\n              <ChevronLeftIcon className={cn(\"size-4\", className)} {...props} />\n            )\n          }\n\n          if (orientation === \"right\") {\n            return (\n              <ChevronRightIcon\n                className={cn(\"size-4\", className)}\n                {...props}\n              />\n            )\n          }\n\n          return (\n            <ChevronDownIcon className={cn(\"size-4\", className)} {...props} />\n          )\n        },\n        DayButton: CalendarDayButton,\n        WeekNumber: ({ children, ...props }) => {\n          return (\n            <td {...props}>\n              <div className=\"flex size-(--cell-size) items-center justify-center text-center\">\n                {children}\n              </div>\n            </td>\n          )\n        },\n        ...components,\n      }}\n      {...props}\n    />\n  )\n}\n\nfunction CalendarDayButton({\n  className,\n  day,\n  modifiers,\n  ...props\n}: React.ComponentProps<typeof DayButton>) {\n  const defaultClassNames = getDefaultClassNames()\n\n  const ref = React.useRef<HTMLButtonElement>(null)\n  React.useEffect(() => {\n    if (modifiers.focused) ref.current?.focus()\n  }, [modifiers.focused])\n\n  return (\n    <Button\n      ref={ref}\n      variant=\"ghost\"\n      size=\"icon\"\n      data-day={day.date.toLocaleDateString()}\n      data-selected-single={\n        modifiers.selected &&\n        !modifiers.range_start &&\n        !modifiers.range_end &&\n        !modifiers.range_middle\n      }\n      data-range-start={modifiers.range_start}\n      data-range-end={modifiers.range_end}\n      data-range-middle={modifiers.range_middle}\n      className={cn(\n        \"data-[selected-single=true]:bg-primary data-[selected-single=true]:text-primary-foreground data-[range-middle=true]:bg-accent data-[range-middle=true]:text-accent-foreground data-[range-start=true]:bg-primary data-[range-start=true]:text-primary-foreground data-[range-end=true]:bg-primary data-[range-end=true]:text-primary-foreground group-data-[focused=true]/day:border-ring group-data-[focused=true]/day:ring-ring/50 dark:hover:text-accent-foreground flex aspect-square size-auto w-full min-w-(--cell-size) flex-col gap-1 leading-none font-normal group-data-[focused=true]/day:relative group-data-[focused=true]/day:z-10 group-data-[focused=true]/day:ring-[3px] data-[range-end=true]:rounded-md data-[range-end=true]:rounded-r-md data-[range-middle=true]:rounded-none data-[range-start=true]:rounded-md data-[range-start=true]:rounded-l-md [&>span]:text-xs [&>span]:opacity-70\",\n        defaultClassNames.day,\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Calendar, CalendarDayButton }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAKA;AAAA;AAEA;AACA;AAXA;;;;;;;AAaA,SAAS,SAAS,EAChB,SAAS,EACT,UAAU,EACV,kBAAkB,IAAI,EACtB,gBAAgB,OAAO,EACvB,gBAAgB,OAAO,EACvB,UAAU,EACV,UAAU,EACV,GAAG,OAGJ;IACC,MAAM,oBAAoB,CAAA,GAAA,wLAAA,CAAA,uBAAoB,AAAD;IAE7C,qBACE,8OAAC,kKAAA,CAAA,YAAS;QACR,iBAAiB;QACjB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA,OAAO,GAAG,CAAC,yCAAyC,CAAC,EACrD,OAAO,GAAG,CAAC,6CAA6C,CAAC,EACzD;QAEF,eAAe;QACf,YAAY;YACV,qBAAqB,CAAC,OACpB,KAAK,cAAc,CAAC,WAAW;oBAAE,OAAO;gBAAQ;YAClD,GAAG,UAAU;QACf;QACA,YAAY;YACV,MAAM,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,SAAS,kBAAkB,IAAI;YACxC,QAAQ,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACP,4CACA,kBAAkB,MAAM;YAE1B,OAAO,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B,kBAAkB,KAAK;YAC/D,KAAK,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACJ,2EACA,kBAAkB,GAAG;YAEvB,iBAAiB,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAChB,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAc,IACxC,+DACA,kBAAkB,eAAe;YAEnC,aAAa,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACZ,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAc,IACxC,+DACA,kBAAkB,WAAW;YAE/B,eAAe,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACd,4EACA,kBAAkB,aAAa;YAEjC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uFACA,kBAAkB,SAAS;YAE7B,eAAe,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACd,uHACA,kBAAkB,aAAa;YAEjC,UAAU,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACT,yCACA,kBAAkB,QAAQ;YAE5B,eAAe,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACd,2BACA,kBAAkB,UACd,YACA,2GACJ,kBAAkB,aAAa;YAEjC,OAAO;YACP,UAAU,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,kBAAkB,QAAQ;YAC/C,SAAS,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACR,iFACA,kBAAkB,OAAO;YAE3B,MAAM,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB,kBAAkB,IAAI;YACnD,oBAAoB,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACnB,+BACA,kBAAkB,kBAAkB;YAEtC,aAAa,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACZ,mDACA,kBAAkB,WAAW;YAE/B,KAAK,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACJ,6LACA,kBAAkB,GAAG;YAEvB,aAAa,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACZ,0BACA,kBAAkB,WAAW;YAE/B,cAAc,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,kBAAkB,YAAY;YAC/D,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B,kBAAkB,SAAS;YACnE,OAAO,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACN,iFACA,kBAAkB,KAAK;YAEzB,SAAS,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACR,6DACA,kBAAkB,OAAO;YAE3B,UAAU,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACT,oCACA,kBAAkB,QAAQ;YAE5B,QAAQ,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa,kBAAkB,MAAM;YAChD,GAAG,UAAU;QACf;QACA,YAAY;YACV,MAAM,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO;gBACrC,qBACE,8OAAC;oBACC,aAAU;oBACV,KAAK;oBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;oBACb,GAAG,KAAK;;;;;;YAGf;YACA,SAAS,CAAC,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,OAAO;gBAC5C,IAAI,gBAAgB,QAAQ;oBAC1B,qBACE,8OAAC,wNAAA,CAAA,kBAAe;wBAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;wBAAa,GAAG,KAAK;;;;;;gBAElE;gBAEA,IAAI,gBAAgB,SAAS;oBAC3B,qBACE,8OAAC,0NAAA,CAAA,mBAAgB;wBACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;wBACvB,GAAG,KAAK;;;;;;gBAGf;gBAEA,qBACE,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;oBAAa,GAAG,KAAK;;;;;;YAElE;YACA,WAAW;YACX,YAAY,CAAC,EAAE,QAAQ,EAAE,GAAG,OAAO;gBACjC,qBACE,8OAAC;oBAAI,GAAG,KAAK;8BACX,cAAA,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;YAIT;YACA,GAAG,UAAU;QACf;QACC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,EACH,SAAS,EACT,GAAG,OACoC;IACvC,MAAM,oBAAoB,CAAA,GAAA,wLAAA,CAAA,uBAAoB,AAAD;IAE7C,MAAM,MAAM,qMAAA,CAAA,SAAY,CAAoB;IAC5C,qMAAA,CAAA,YAAe,CAAC;QACd,IAAI,UAAU,OAAO,EAAE,IAAI,OAAO,EAAE;IACtC,GAAG;QAAC,UAAU,OAAO;KAAC;IAEtB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,KAAK;QACL,SAAQ;QACR,MAAK;QACL,YAAU,IAAI,IAAI,CAAC,kBAAkB;QACrC,wBACE,UAAU,QAAQ,IAClB,CAAC,UAAU,WAAW,IACtB,CAAC,UAAU,SAAS,IACpB,CAAC,UAAU,YAAY;QAEzB,oBAAkB,UAAU,WAAW;QACvC,kBAAgB,UAAU,SAAS;QACnC,qBAAmB,UAAU,YAAY;QACzC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,o3BACA,kBAAkB,GAAG,EACrB;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 406, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/personal%20projects/CREATEXP/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Popover({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />\n}\n\nfunction PopoverTrigger({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />\n}\n\nfunction PopoverContent({\n  className,\n  align = \"center\",\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\n  return (\n    <PopoverPrimitive.Portal>\n      <PopoverPrimitive.Content\n        data-slot=\"popover-content\"\n        align={align}\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\n          className\n        )}\n        {...props}\n      />\n    </PopoverPrimitive.Portal>\n  )\n}\n\nfunction PopoverAnchor({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />\n}\n\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBAAO,8OAAC,mKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,mKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE", "debugId": null}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/personal%20projects/CREATEXP/src/components/DateRangeFilter.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Calendar as CalendarIcon, X } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Calendar } from '@/components/ui/calendar';\nimport {\n  Popover,\n  PopoverContent,\n  PopoverTrigger,\n} from '@/components/ui/popover';\nimport { Badge } from '@/components/ui/badge';\n\nexport interface DateRange {\n  from?: Date;\n  to?: Date;\n}\n\ninterface DateRangeFilterProps {\n  label: string;\n  value: DateRange;\n  onChange: (range: DateRange) => void;\n}\n\nexport function DateRangeFilter({ label, value, onChange }: DateRangeFilterProps) {\n  const [isOpen, setIsOpen] = useState(false);\n\n  const formatDateRange = (range: DateRange) => {\n    if (!range.from) return 'Select dates';\n    if (!range.to) return range.from.toLocaleDateString();\n    return `${range.from.toLocaleDateString()} - ${range.to.toLocaleDateString()}`;\n  };\n\n  const clearFilter = () => {\n    onChange({ from: undefined, to: undefined });\n  };\n\n  const hasFilter = value.from || value.to;\n\n  return (\n    <div className=\"flex items-center space-x-2\">\n      <Popover open={isOpen} onOpenChange={setIsOpen}>\n        <PopoverTrigger asChild>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            className={`justify-start text-left font-normal ${\n              hasFilter ? 'bg-blue-50 border-blue-200' : ''\n            }`}\n          >\n            <CalendarIcon className=\"mr-2 h-4 w-4\" />\n            {label}\n          </Button>\n        </PopoverTrigger>\n        <PopoverContent className=\"w-auto p-0\" align=\"start\">\n          <div className=\"p-3 border-b\">\n            <h4 className=\"font-medium\">{label}</h4>\n            <p className=\"text-sm text-gray-600\">{formatDateRange(value)}</p>\n          </div>\n          <Calendar\n            mode=\"range\"\n            selected={value}\n            onSelect={(range) => {\n              if (range) {\n                onChange(range);\n              }\n            }}\n            numberOfMonths={1}\n            className=\"p-3\"\n          />\n          <div className=\"p-3 border-t flex justify-between\">\n            <Button variant=\"outline\" size=\"sm\" onClick={clearFilter}>\n              Clear\n            </Button>\n            <Button size=\"sm\" onClick={() => setIsOpen(false)}>\n              Apply\n            </Button>\n          </div>\n        </PopoverContent>\n      </Popover>\n      \n      {hasFilter && (\n        <Badge variant=\"secondary\" className=\"flex items-center space-x-1\">\n          <span className=\"text-xs\">{formatDateRange(value)}</span>\n          <button onClick={clearFilter} className=\"ml-1\">\n            <X className=\"h-3 w-3\" />\n          </button>\n        </Badge>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AAKA;AAXA;;;;;;;;AAwBO,SAAS,gBAAgB,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAwB;IAC9E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,MAAM,IAAI,EAAE,OAAO;QACxB,IAAI,CAAC,MAAM,EAAE,EAAE,OAAO,MAAM,IAAI,CAAC,kBAAkB;QACnD,OAAO,GAAG,MAAM,IAAI,CAAC,kBAAkB,GAAG,GAAG,EAAE,MAAM,EAAE,CAAC,kBAAkB,IAAI;IAChF;IAEA,MAAM,cAAc;QAClB,SAAS;YAAE,MAAM;YAAW,IAAI;QAAU;IAC5C;IAEA,MAAM,YAAY,MAAM,IAAI,IAAI,MAAM,EAAE;IAExC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,mIAAA,CAAA,UAAO;gBAAC,MAAM;gBAAQ,cAAc;;kCACnC,8OAAC,mIAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAW,CAAC,oCAAoC,EAC9C,YAAY,+BAA+B,IAC3C;;8CAEF,8OAAC,0MAAA,CAAA,WAAY;oCAAC,WAAU;;;;;;gCACvB;;;;;;;;;;;;kCAGL,8OAAC,mIAAA,CAAA,iBAAc;wBAAC,WAAU;wBAAa,OAAM;;0CAC3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAe;;;;;;kDAC7B,8OAAC;wCAAE,WAAU;kDAAyB,gBAAgB;;;;;;;;;;;;0CAExD,8OAAC,oIAAA,CAAA,WAAQ;gCACP,MAAK;gCACL,UAAU;gCACV,UAAU,CAAC;oCACT,IAAI,OAAO;wCACT,SAAS;oCACX;gCACF;gCACA,gBAAgB;gCAChB,WAAU;;;;;;0CAEZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS;kDAAa;;;;;;kDAG1D,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,SAAS,IAAM,UAAU;kDAAQ;;;;;;;;;;;;;;;;;;;;;;;;YAOxD,2BACC,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAY,WAAU;;kCACnC,8OAAC;wBAAK,WAAU;kCAAW,gBAAgB;;;;;;kCAC3C,8OAAC;wBAAO,SAAS;wBAAa,WAAU;kCACtC,cAAA,8OAAC,4LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAMzB", "debugId": null}}, {"offset": {"line": 667, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/personal%20projects/CREATEXP/src/components/ClientTable.tsx"], "sourcesContent": ["'use client';\n\nimport { Client, ClientFilter, DateFilters } from '@/types/client';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from '@/components/ui/table';\nimport { Search, Filter, Plus } from 'lucide-react';\nimport { DateRangeFilter, DateRange } from '@/components/DateRangeFilter';\n\ninterface ClientTableProps {\n  clients: Client[];\n  filter: ClientFilter;\n  onFilterChange: (filter: ClientFilter) => void;\n  onToggleSort?: () => void;\n  sortCriteriaCount?: number;\n  searchQuery: string;\n  debouncedSearchQuery: string;\n  onSearchChange: (query: string) => void;\n  dateFilters: DateFilters;\n  onDateFiltersChange: (filters: DateFilters) => void;\n}\n\nexport function ClientTable({\n  clients,\n  filter,\n  onFilterChange,\n  onToggleSort,\n  sortCriteriaCount = 0,\n  searchQuery,\n  debouncedSearchQuery,\n  onSearchChange,\n  dateFilters,\n  onDateFiltersChange\n}: ClientTableProps) {\n  const formatDate = (date: Date) => {\n    return date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n    });\n  };\n\n  const getStatusBadgeVariant = (status: string) => {\n    return status === 'Active' ? 'default' : 'secondary';\n  };\n\n  const filteredClients = clients.filter(client => {\n    // Filter by client type\n    const typeMatch = filter === 'All' || client.clientType === filter;\n\n    // Filter by search query (using debounced value)\n    const searchMatch = !debouncedSearchQuery ||\n      client.clientName.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||\n      client.email.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||\n      client.id.toString().includes(debouncedSearchQuery);\n\n    // Filter by created date range\n    const createdAtMatch = (!dateFilters?.createdAt?.from || client.createdAt >= dateFilters.createdAt.from) &&\n      (!dateFilters?.createdAt?.to || client.createdAt <= dateFilters.createdAt.to);\n\n    // Filter by updated date range\n    const updatedAtMatch = (!dateFilters?.updatedAt?.from || client.updatedAt >= dateFilters.updatedAt.from) &&\n      (!dateFilters?.updatedAt?.to || client.updatedAt <= dateFilters.updatedAt.to);\n\n    return typeMatch && searchMatch && createdAtMatch && updatedAtMatch;\n  });\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h1 className=\"text-2xl font-semibold\">Clients</h1>\n        <Button className=\"bg-black text-white hover:bg-gray-800\">\n          <Plus className=\"w-4 h-4 mr-2\" />\n          Add Client\n        </Button>\n      </div>\n\n      {/* Filters */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"flex items-center space-x-2\">\n            {(['All', 'Individual', 'Company'] as ClientFilter[]).map((filterOption) => (\n              <button\n                key={filterOption}\n                onClick={() => onFilterChange(filterOption)}\n                className={`px-3 py-1 text-sm rounded-md transition-colors ${\n                  filter === filterOption\n                    ? 'bg-gray-100 text-gray-900 border-b-2 border-gray-900'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                {filterOption}\n              </button>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"relative\">\n            <Search className=\"w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search clients...\"\n              value={searchQuery}\n              onChange={(e) => onSearchChange(e.target.value)}\n              className=\"pl-10 pr-4 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            />\n          </div>\n\n          <DateRangeFilter\n            label=\"Created\"\n            value={dateFilters?.createdAt || { from: undefined, to: undefined }}\n            onChange={(range) => onDateFiltersChange({ ...dateFilters, createdAt: range })}\n          />\n\n          <DateRangeFilter\n            label=\"Updated\"\n            value={dateFilters?.updatedAt || { from: undefined, to: undefined }}\n            onChange={(range) => onDateFiltersChange({ ...dateFilters, updatedAt: range })}\n          />\n\n          <Button variant=\"outline\" size=\"sm\" onClick={onToggleSort}>\n            <Filter className=\"w-4 h-4 mr-2\" />\n            {sortCriteriaCount}\n          </Button>\n        </div>\n      </div>\n\n      {/* Table */}\n      <div className=\"border rounded-lg\">\n        <Table>\n          <TableHeader>\n            <TableRow>\n              <TableHead className=\"w-12\">\n                <input type=\"checkbox\" className=\"rounded\" />\n              </TableHead>\n              <TableHead>Client ID</TableHead>\n              <TableHead>Client Name</TableHead>\n              <TableHead>Client Type</TableHead>\n              <TableHead>Email</TableHead>\n              <TableHead>Created At</TableHead>\n              <TableHead>Updated At</TableHead>\n              <TableHead>Status</TableHead>\n              <TableHead>Updated By</TableHead>\n            </TableRow>\n          </TableHeader>\n          <TableBody>\n            {filteredClients.map((client) => (\n              <TableRow key={client.id} className=\"hover:bg-gray-50 transition-colors\">\n                <TableCell>\n                  <input type=\"checkbox\" className=\"rounded\" />\n                </TableCell>\n                <TableCell className=\"text-blue-600 font-medium\">\n                  {client.id}\n                </TableCell>\n                <TableCell className=\"font-medium\">\n                  {client.clientName}\n                </TableCell>\n                <TableCell>\n                  {client.clientType}\n                </TableCell>\n                <TableCell className=\"text-gray-600\">\n                  {client.email}\n                </TableCell>\n                <TableCell className=\"text-gray-600\">\n                  {formatDate(client.createdAt)}\n                </TableCell>\n                <TableCell className=\"text-gray-600\">\n                  {formatDate(client.updatedAt)}\n                </TableCell>\n                <TableCell>\n                  <Badge variant={getStatusBadgeVariant(client.status)}>\n                    {client.status}\n                  </Badge>\n                </TableCell>\n                <TableCell className=\"text-gray-600\">\n                  hello world\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAQA;AAAA;AAAA;AACA;AAdA;;;;;;;AA6BO,SAAS,YAAY,EAC1B,OAAO,EACP,MAAM,EACN,cAAc,EACd,YAAY,EACZ,oBAAoB,CAAC,EACrB,WAAW,EACX,oBAAoB,EACpB,cAAc,EACd,WAAW,EACX,mBAAmB,EACF;IACjB,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAO,WAAW,WAAW,YAAY;IAC3C;IAEA,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA;QACrC,wBAAwB;QACxB,MAAM,YAAY,WAAW,SAAS,OAAO,UAAU,KAAK;QAE5D,iDAAiD;QACjD,MAAM,cAAc,CAAC,wBACnB,OAAO,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,qBAAqB,WAAW,OACzE,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,qBAAqB,WAAW,OACpE,OAAO,EAAE,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAEhC,+BAA+B;QAC/B,MAAM,iBAAiB,CAAC,CAAC,aAAa,WAAW,QAAQ,OAAO,SAAS,IAAI,YAAY,SAAS,CAAC,IAAI,KACrG,CAAC,CAAC,aAAa,WAAW,MAAM,OAAO,SAAS,IAAI,YAAY,SAAS,CAAC,EAAE;QAE9E,+BAA+B;QAC/B,MAAM,iBAAiB,CAAC,CAAC,aAAa,WAAW,QAAQ,OAAO,SAAS,IAAI,YAAY,SAAS,CAAC,IAAI,KACrG,CAAC,CAAC,aAAa,WAAW,MAAM,OAAO,SAAS,IAAI,YAAY,SAAS,CAAC,EAAE;QAE9E,OAAO,aAAa,eAAe,kBAAkB;IACvD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyB;;;;;;kCACvC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,WAAU;;0CAChB,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,AAAC;gCAAC;gCAAO;gCAAc;6BAAU,CAAoB,GAAG,CAAC,CAAC,6BACzD,8OAAC;oCAEC,SAAS,IAAM,eAAe;oCAC9B,WAAW,CAAC,+CAA+C,EACzD,WAAW,eACP,yDACA,qCACJ;8CAED;mCARI;;;;;;;;;;;;;;;kCAcb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;0CAId,8OAAC,qIAAA,CAAA,kBAAe;gCACd,OAAM;gCACN,OAAO,aAAa,aAAa;oCAAE,MAAM;oCAAW,IAAI;gCAAU;gCAClE,UAAU,CAAC,QAAU,oBAAoB;wCAAE,GAAG,WAAW;wCAAE,WAAW;oCAAM;;;;;;0CAG9E,8OAAC,qIAAA,CAAA,kBAAe;gCACd,OAAM;gCACN,OAAO,aAAa,aAAa;oCAAE,MAAM;oCAAW,IAAI;gCAAU;gCAClE,UAAU,CAAC,QAAU,oBAAoB;wCAAE,GAAG,WAAW;wCAAE,WAAW;oCAAM;;;;;;0CAG9E,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,SAAS;;kDAC3C,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCACjB;;;;;;;;;;;;;;;;;;;0BAMP,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;;sCACJ,8OAAC,iIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;kDACP,8OAAC,iIAAA,CAAA,YAAS;wCAAC,WAAU;kDACnB,cAAA,8OAAC;4CAAM,MAAK;4CAAW,WAAU;;;;;;;;;;;kDAEnC,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;;;;;;;sCAGf,8OAAC,iIAAA,CAAA,YAAS;sCACP,gBAAgB,GAAG,CAAC,CAAC,uBACpB,8OAAC,iIAAA,CAAA,WAAQ;oCAAiB,WAAU;;sDAClC,8OAAC,iIAAA,CAAA,YAAS;sDACR,cAAA,8OAAC;gDAAM,MAAK;gDAAW,WAAU;;;;;;;;;;;sDAEnC,8OAAC,iIAAA,CAAA,YAAS;4CAAC,WAAU;sDAClB,OAAO,EAAE;;;;;;sDAEZ,8OAAC,iIAAA,CAAA,YAAS;4CAAC,WAAU;sDAClB,OAAO,UAAU;;;;;;sDAEpB,8OAAC,iIAAA,CAAA,YAAS;sDACP,OAAO,UAAU;;;;;;sDAEpB,8OAAC,iIAAA,CAAA,YAAS;4CAAC,WAAU;sDAClB,OAAO,KAAK;;;;;;sDAEf,8OAAC,iIAAA,CAAA,YAAS;4CAAC,WAAU;sDAClB,WAAW,OAAO,SAAS;;;;;;sDAE9B,8OAAC,iIAAA,CAAA,YAAS;4CAAC,WAAU;sDAClB,WAAW,OAAO,SAAS;;;;;;sDAE9B,8OAAC,iIAAA,CAAA,YAAS;sDACR,cAAA,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAS,sBAAsB,OAAO,MAAM;0DAChD,OAAO,MAAM;;;;;;;;;;;sDAGlB,8OAAC,iIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAgB;;;;;;;mCA3BxB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCtC", "debugId": null}}, {"offset": {"line": 1074, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/personal%20projects/CREATEXP/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1334, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/personal%20projects/CREATEXP/src/components/SortPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport {\n  DndContext,\n  closestCenter,\n  KeyboardSensor,\n  PointerSensor,\n  useSensor,\n  useSensors,\n  DragEndEvent,\n} from '@dnd-kit/core';\nimport {\n  arrayMove,\n  SortableContext,\n  sortableKeyboardCoordinates,\n  verticalListSortingStrategy,\n} from '@dnd-kit/sortable';\nimport {\n  useSortable,\n} from '@dnd-kit/sortable';\nimport { CSS } from '@dnd-kit/utilities';\nimport { SortCriterion, SortField, SortDirection } from '@/types/client';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport { \n  GripVertical, \n  ChevronUp, \n  ChevronDown, \n  X, \n  Plus,\n  ArrowUpDown,\n  Calendar,\n  User,\n  Hash\n} from 'lucide-react';\n\ninterface SortPanelProps {\n  criteria: SortCriterion[];\n  onCriteriaChange: (criteria: SortCriterion[]) => void;\n  onApplySort: () => void;\n  onClearAll: () => void;\n  onClose?: () => void;\n}\n\nconst sortFieldOptions: { value: SortField; label: string; icon: React.ReactNode }[] = [\n  { value: 'clientName', label: 'Client Name', icon: <User className=\"w-4 h-4\" /> },\n  { value: 'createdAt', label: 'Created At', icon: <Calendar className=\"w-4 h-4\" /> },\n  { value: 'updatedAt', label: 'Updated At', icon: <Calendar className=\"w-4 h-4\" /> },\n  { value: 'clientId', label: 'Client ID', icon: <Hash className=\"w-4 h-4\" /> },\n];\n\ninterface SortableItemProps {\n  criterion: SortCriterion;\n  onDirectionToggle: (id: string) => void;\n  onRemove: (id: string) => void;\n}\n\nfunction SortableItem({ criterion, onDirectionToggle, onRemove }: SortableItemProps) {\n  const {\n    attributes,\n    listeners,\n    setNodeRef,\n    transform,\n    transition,\n    isDragging,\n  } = useSortable({ id: criterion.id });\n\n  const style = {\n    transform: CSS.Transform.toString(transform),\n    transition,\n  };\n\n  const fieldOption = sortFieldOptions.find(option => option.value === criterion.field);\n\n  return (\n    <div\n      ref={setNodeRef}\n      style={style}\n      className={`flex items-center justify-between p-3 bg-white border rounded-lg transition-all duration-200 ${\n        isDragging ? 'shadow-lg opacity-50 scale-105' : 'shadow-sm hover:shadow-md'\n      }`}\n    >\n      <div className=\"flex items-center space-x-3\">\n        <button\n          className=\"cursor-grab active:cursor-grabbing text-gray-400 hover:text-gray-600\"\n          {...attributes}\n          {...listeners}\n        >\n          <GripVertical className=\"w-4 h-4\" />\n        </button>\n        \n        <div className=\"flex items-center space-x-2\">\n          {fieldOption?.icon}\n          <span className=\"font-medium\">{criterion.label}</span>\n        </div>\n      </div>\n\n      <div className=\"flex items-center space-x-2\">\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => onDirectionToggle(criterion.id)}\n          className=\"flex items-center space-x-1\"\n        >\n          {criterion.direction === 'asc' ? (\n            <>\n              <ChevronUp className=\"w-4 h-4\" />\n              <span>A-Z</span>\n            </>\n          ) : (\n            <>\n              <ChevronDown className=\"w-4 h-4\" />\n              <span>Z-A</span>\n            </>\n          )}\n        </Button>\n        \n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={() => onRemove(criterion.id)}\n          className=\"text-gray-400 hover:text-red-500\"\n        >\n          <X className=\"w-4 h-4\" />\n        </Button>\n      </div>\n    </div>\n  );\n}\n\nexport function SortPanel({ criteria, onCriteriaChange, onApplySort, onClearAll, onClose }: SortPanelProps) {\n  const sensors = useSensors(\n    useSensor(PointerSensor),\n    useSensor(KeyboardSensor, {\n      coordinateGetter: sortableKeyboardCoordinates,\n    })\n  );\n\n  const handleDragEnd = (event: DragEndEvent) => {\n    const { active, over } = event;\n\n    if (active.id !== over?.id) {\n      const oldIndex = criteria.findIndex(item => item.id === active.id);\n      const newIndex = criteria.findIndex(item => item.id === over?.id);\n      \n      onCriteriaChange(arrayMove(criteria, oldIndex, newIndex));\n    }\n  };\n\n  const handleDirectionToggle = (id: string) => {\n    const updatedCriteria = criteria.map(criterion =>\n      criterion.id === id\n        ? { ...criterion, direction: criterion.direction === 'asc' ? 'desc' : 'asc' as SortDirection }\n        : criterion\n    );\n    onCriteriaChange(updatedCriteria);\n  };\n\n  const handleRemove = (id: string) => {\n    onCriteriaChange(criteria.filter(criterion => criterion.id !== id));\n  };\n\n  const handleAddCriterion = (field: SortField) => {\n    const fieldOption = sortFieldOptions.find(option => option.value === field);\n    if (!fieldOption) return;\n\n    // Check if this field is already being sorted\n    if (criteria.some(criterion => criterion.field === field)) return;\n\n    const newCriterion: SortCriterion = {\n      id: `${field}-${Date.now()}`,\n      field,\n      direction: 'asc',\n      label: fieldOption.label,\n    };\n\n    onCriteriaChange([...criteria, newCriterion]);\n  };\n\n  const availableFields = sortFieldOptions.filter(\n    option => !criteria.some(criterion => criterion.field === option.value)\n  );\n\n  return (\n    <div className=\"w-80 bg-gray-50 border-l p-4 space-y-4\">\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-2\">\n          <ArrowUpDown className=\"w-5 h-5\" />\n          <h3 className=\"font-semibold\">Sort By</h3>\n        </div>\n        {onClose && (\n          <Button variant=\"ghost\" size=\"sm\" onClick={onClose}>\n            <X className=\"w-4 h-4\" />\n          </Button>\n        )}\n      </div>\n\n      <DndContext\n        sensors={sensors}\n        collisionDetection={closestCenter}\n        onDragEnd={handleDragEnd}\n      >\n        <SortableContext items={criteria.map(c => c.id)} strategy={verticalListSortingStrategy}>\n          <div className=\"space-y-2\">\n            {criteria.map((criterion) => (\n              <SortableItem\n                key={criterion.id}\n                criterion={criterion}\n                onDirectionToggle={handleDirectionToggle}\n                onRemove={handleRemove}\n              />\n            ))}\n          </div>\n        </SortableContext>\n      </DndContext>\n\n      {availableFields.length > 0 && (\n        <DropdownMenu>\n          <DropdownMenuTrigger asChild>\n            <Button variant=\"outline\" className=\"w-full\">\n              <Plus className=\"w-4 h-4 mr-2\" />\n              Add Sort Criterion\n            </Button>\n          </DropdownMenuTrigger>\n          <DropdownMenuContent className=\"w-56\">\n            {availableFields.map((field) => (\n              <DropdownMenuItem\n                key={field.value}\n                onClick={() => handleAddCriterion(field.value)}\n                className=\"flex items-center space-x-2\"\n              >\n                {field.icon}\n                <span>{field.label}</span>\n              </DropdownMenuItem>\n            ))}\n          </DropdownMenuContent>\n        </DropdownMenu>\n      )}\n\n      <div className=\"flex space-x-2 pt-4 border-t\">\n        <Button onClick={onClearAll} variant=\"outline\" className=\"flex-1\">\n          Clear all\n        </Button>\n        <Button onClick={onApplySort} className=\"flex-1 bg-black text-white hover:bg-gray-800\">\n          Apply Sort\n        </Button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AASA;AASA;AAEA;AAEA;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA/BA;;;;;;;;;AAmDA,MAAM,mBAAiF;IACrF;QAAE,OAAO;QAAc,OAAO;QAAe,oBAAM,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IAAa;IAChF;QAAE,OAAO;QAAa,OAAO;QAAc,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAAa;IAClF;QAAE,OAAO;QAAa,OAAO;QAAc,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAAa;IAClF;QAAE,OAAO;QAAY,OAAO;QAAa,oBAAM,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IAAa;CAC7E;AAQD,SAAS,aAAa,EAAE,SAAS,EAAE,iBAAiB,EAAE,QAAQ,EAAqB;IACjF,MAAM,EACJ,UAAU,EACV,SAAS,EACT,UAAU,EACV,SAAS,EACT,UAAU,EACV,UAAU,EACX,GAAG,CAAA,GAAA,mKAAA,CAAA,cAAW,AAAD,EAAE;QAAE,IAAI,UAAU,EAAE;IAAC;IAEnC,MAAM,QAAQ;QACZ,WAAW,qKAAA,CAAA,MAAG,CAAC,SAAS,CAAC,QAAQ,CAAC;QAClC;IACF;IAEA,MAAM,cAAc,iBAAiB,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK,UAAU,KAAK;IAEpF,qBACE,8OAAC;QACC,KAAK;QACL,OAAO;QACP,WAAW,CAAC,6FAA6F,EACvG,aAAa,mCAAmC,6BAChD;;0BAEF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAU;wBACT,GAAG,UAAU;wBACb,GAAG,SAAS;kCAEb,cAAA,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;kCAG1B,8OAAC;wBAAI,WAAU;;4BACZ,aAAa;0CACd,8OAAC;gCAAK,WAAU;0CAAe,UAAU,KAAK;;;;;;;;;;;;;;;;;;0BAIlD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,kBAAkB,UAAU,EAAE;wBAC7C,WAAU;kCAET,UAAU,SAAS,KAAK,sBACvB;;8CACE,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;8CAAK;;;;;;;yDAGR;;8CACE,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;8CAAK;;;;;;;;;;;;;kCAKZ,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,SAAS,UAAU,EAAE;wBACpC,WAAU;kCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKvB;AAEO,SAAS,UAAU,EAAE,QAAQ,EAAE,gBAAgB,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAkB;IACxG,MAAM,UAAU,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD,EACvB,CAAA,GAAA,2JAAA,CAAA,YAAS,AAAD,EAAE,2JAAA,CAAA,gBAAa,GACvB,CAAA,GAAA,2JAAA,CAAA,YAAS,AAAD,EAAE,2JAAA,CAAA,iBAAc,EAAE;QACxB,kBAAkB,mKAAA,CAAA,8BAA2B;IAC/C;IAGF,MAAM,gBAAgB,CAAC;QACrB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QAEzB,IAAI,OAAO,EAAE,KAAK,MAAM,IAAI;YAC1B,MAAM,WAAW,SAAS,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,EAAE;YACjE,MAAM,WAAW,SAAS,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,MAAM;YAE9D,iBAAiB,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,UAAU,UAAU;QACjD;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,MAAM,kBAAkB,SAAS,GAAG,CAAC,CAAA,YACnC,UAAU,EAAE,KAAK,KACb;gBAAE,GAAG,SAAS;gBAAE,WAAW,UAAU,SAAS,KAAK,QAAQ,SAAS;YAAuB,IAC3F;QAEN,iBAAiB;IACnB;IAEA,MAAM,eAAe,CAAC;QACpB,iBAAiB,SAAS,MAAM,CAAC,CAAA,YAAa,UAAU,EAAE,KAAK;IACjE;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,cAAc,iBAAiB,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK;QACrE,IAAI,CAAC,aAAa;QAElB,8CAA8C;QAC9C,IAAI,SAAS,IAAI,CAAC,CAAA,YAAa,UAAU,KAAK,KAAK,QAAQ;QAE3D,MAAM,eAA8B;YAClC,IAAI,GAAG,MAAM,CAAC,EAAE,KAAK,GAAG,IAAI;YAC5B;YACA,WAAW;YACX,OAAO,YAAY,KAAK;QAC1B;QAEA,iBAAiB;eAAI;YAAU;SAAa;IAC9C;IAEA,MAAM,kBAAkB,iBAAiB,MAAM,CAC7C,CAAA,SAAU,CAAC,SAAS,IAAI,CAAC,CAAA,YAAa,UAAU,KAAK,KAAK,OAAO,KAAK;IAGxE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,wNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC;gCAAG,WAAU;0CAAgB;;;;;;;;;;;;oBAE/B,yBACC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAK,SAAS;kCACzC,cAAA,8OAAC,4LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKnB,8OAAC,2JAAA,CAAA,aAAU;gBACT,SAAS;gBACT,oBAAoB,2JAAA,CAAA,gBAAa;gBACjC,WAAW;0BAEX,cAAA,8OAAC,mKAAA,CAAA,kBAAe;oBAAC,OAAO,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;oBAAG,UAAU,mKAAA,CAAA,8BAA2B;8BACpF,cAAA,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,0BACb,8OAAC;gCAEC,WAAW;gCACX,mBAAmB;gCACnB,UAAU;+BAHL,UAAU,EAAE;;;;;;;;;;;;;;;;;;;;YAU1B,gBAAgB,MAAM,GAAG,mBACxB,8OAAC,4IAAA,CAAA,eAAY;;kCACX,8OAAC,4IAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,WAAU;;8CAClC,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIrC,8OAAC,4IAAA,CAAA,sBAAmB;wBAAC,WAAU;kCAC5B,gBAAgB,GAAG,CAAC,CAAC,sBACpB,8OAAC,4IAAA,CAAA,mBAAgB;gCAEf,SAAS,IAAM,mBAAmB,MAAM,KAAK;gCAC7C,WAAU;;oCAET,MAAM,IAAI;kDACX,8OAAC;kDAAM,MAAM,KAAK;;;;;;;+BALb,MAAM,KAAK;;;;;;;;;;;;;;;;0BAY1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAY,SAAQ;wBAAU,WAAU;kCAAS;;;;;;kCAGlE,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAa,WAAU;kCAA+C;;;;;;;;;;;;;;;;;;AAM/F", "debugId": null}}, {"offset": {"line": 1763, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/personal%20projects/CREATEXP/src/data/mockClients.ts"], "sourcesContent": ["import { Client } from '@/types/client';\n\n// Mock client data\nexport const mockClients: Client[] = [\n  {\n    id: 20,\n    clientName: '<PERSON>',\n    clientType: 'Individual',\n    email: 'johndo<PERSON>@email.com',\n    createdAt: new Date('2024-01-15T10:30:00Z'),\n    updatedAt: new Date('2024-07-20T14:45:00Z'),\n    status: 'Active'\n  },\n  {\n    id: 21,\n    clientName: 'Test Test',\n    clientType: 'Individual',\n    email: '<EMAIL>',\n    createdAt: new Date('2024-02-10T09:15:00Z'),\n    updatedAt: new Date('2024-07-25T16:20:00Z'),\n    status: 'Active'\n  },\n  {\n    id: 22,\n    clientName: 'Acme Corporation',\n    clientType: 'Company',\n    email: '<EMAIL>',\n    createdAt: new Date('2024-01-05T08:00:00Z'),\n    updatedAt: new Date('2024-07-28T11:30:00Z'),\n    status: 'Active'\n  },\n  {\n    id: 23,\n    clientName: '<PERSON>',\n    clientType: 'Individual',\n    email: '<EMAIL>',\n    createdAt: new Date('2024-03-20T13:45:00Z'),\n    updatedAt: new Date('2024-07-22T10:15:00Z'),\n    status: 'Inactive'\n  },\n  {\n    id: 24,\n    clientName: 'Tech Solutions Inc',\n    clientType: 'Company',\n    email: '<EMAIL>',\n    createdAt: new Date('2024-01-30T15:20:00Z'),\n    updatedAt: new Date('2024-07-26T09:45:00Z'),\n    status: 'Active'\n  },\n  {\n    id: 25,\n    clientName: 'Bob Johnson',\n    clientType: 'Individual',\n    email: '<EMAIL>',\n    createdAt: new Date('2024-04-12T11:10:00Z'),\n    updatedAt: new Date('2024-07-24T15:30:00Z'),\n    status: 'Active'\n  },\n  {\n    id: 26,\n    clientName: 'Global Enterprises',\n    clientType: 'Company',\n    email: '<EMAIL>',\n    createdAt: new Date('2024-02-28T16:40:00Z'),\n    updatedAt: new Date('2024-07-29T12:20:00Z'),\n    status: 'Active'\n  },\n  {\n    id: 27,\n    clientName: 'Alice Brown',\n    clientType: 'Individual',\n    email: '<EMAIL>',\n    createdAt: new Date('2024-05-08T14:25:00Z'),\n    updatedAt: new Date('2024-07-23T17:10:00Z'),\n    status: 'Inactive'\n  }\n];\n"], "names": [], "mappings": ";;;AAGO,MAAM,cAAwB;IACnC;QACE,IAAI;QACJ,YAAY;QACZ,YAAY;QACZ,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ;IACV;IACA;QACE,IAAI;QACJ,YAAY;QACZ,YAAY;QACZ,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ;IACV;IACA;QACE,IAAI;QACJ,YAAY;QACZ,YAAY;QACZ,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ;IACV;IACA;QACE,IAAI;QACJ,YAAY;QACZ,YAAY;QACZ,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ;IACV;IACA;QACE,IAAI;QACJ,YAAY;QACZ,YAAY;QACZ,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ;IACV;IACA;QACE,IAAI;QACJ,YAAY;QACZ,YAAY;QACZ,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ;IACV;IACA;QACE,IAAI;QACJ,YAAY;QACZ,YAAY;QACZ,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ;IACV;IACA;QACE,IAAI;QACJ,YAAY;QACZ,YAAY;QACZ,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ;IACV;CACD", "debugId": null}}, {"offset": {"line": 1844, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/personal%20projects/CREATEXP/src/utils/sorting.ts"], "sourcesContent": ["import { Client, SortCriterion } from '@/types/client';\n\n/**\n * Multi-sort function that applies multiple sort criteria in order of priority\n */\nexport function multiSort(clients: Client[], criteria: SortCriterion[]): Client[] {\n  if (criteria.length === 0) {\n    return [...clients];\n  }\n\n  return [...clients].sort((a, b) => {\n    for (const criterion of criteria) {\n      let aValue: any;\n      let bValue: any;\n\n      // Get the values to compare based on the field\n      switch (criterion.field) {\n        case 'clientName':\n          aValue = a.clientName.toLowerCase();\n          bValue = b.clientName.toLowerCase();\n          break;\n        case 'createdAt':\n          aValue = a.createdAt.getTime();\n          bValue = b.createdAt.getTime();\n          break;\n        case 'updatedAt':\n          aValue = a.updatedAt.getTime();\n          bValue = b.updatedAt.getTime();\n          break;\n        case 'clientId':\n          aValue = a.id;\n          bValue = b.id;\n          break;\n        default:\n          continue;\n      }\n\n      // Compare the values\n      let comparison = 0;\n      \n      if (aValue < bValue) {\n        comparison = -1;\n      } else if (aValue > bValue) {\n        comparison = 1;\n      }\n\n      // If values are equal, continue to next criterion\n      if (comparison === 0) {\n        continue;\n      }\n\n      // Apply sort direction\n      return criterion.direction === 'asc' ? comparison : -comparison;\n    }\n\n    // If all criteria result in equality, maintain original order\n    return 0;\n  });\n}\n\n/**\n * Helper function to get display value for a sort field\n */\nexport function getSortDisplayValue(client: Client, field: string): string {\n  switch (field) {\n    case 'clientName':\n      return client.clientName;\n    case 'createdAt':\n      return client.createdAt.toLocaleDateString();\n    case 'updatedAt':\n      return client.updatedAt.toLocaleDateString();\n    case 'clientId':\n      return client.id.toString();\n    default:\n      return '';\n  }\n}\n"], "names": [], "mappings": ";;;;AAKO,SAAS,UAAU,OAAiB,EAAE,QAAyB;IACpE,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,OAAO;eAAI;SAAQ;IACrB;IAEA,OAAO;WAAI;KAAQ,CAAC,IAAI,CAAC,CAAC,GAAG;QAC3B,KAAK,MAAM,aAAa,SAAU;YAChC,IAAI;YACJ,IAAI;YAEJ,+CAA+C;YAC/C,OAAQ,UAAU,KAAK;gBACrB,KAAK;oBACH,SAAS,EAAE,UAAU,CAAC,WAAW;oBACjC,SAAS,EAAE,UAAU,CAAC,WAAW;oBACjC;gBACF,KAAK;oBACH,SAAS,EAAE,SAAS,CAAC,OAAO;oBAC5B,SAAS,EAAE,SAAS,CAAC,OAAO;oBAC5B;gBACF,KAAK;oBACH,SAAS,EAAE,SAAS,CAAC,OAAO;oBAC5B,SAAS,EAAE,SAAS,CAAC,OAAO;oBAC5B;gBACF,KAAK;oBACH,SAAS,EAAE,EAAE;oBACb,SAAS,EAAE,EAAE;oBACb;gBACF;oBACE;YACJ;YAEA,qBAAqB;YACrB,IAAI,aAAa;YAEjB,IAAI,SAAS,QAAQ;gBACnB,aAAa,CAAC;YAChB,OAAO,IAAI,SAAS,QAAQ;gBAC1B,aAAa;YACf;YAEA,kDAAkD;YAClD,IAAI,eAAe,GAAG;gBACpB;YACF;YAEA,uBAAuB;YACvB,OAAO,UAAU,SAAS,KAAK,QAAQ,aAAa,CAAC;QACvD;QAEA,8DAA8D;QAC9D,OAAO;IACT;AACF;AAKO,SAAS,oBAAoB,MAAc,EAAE,KAAa;IAC/D,OAAQ;QACN,KAAK;YACH,OAAO,OAAO,UAAU;QAC1B,KAAK;YACH,OAAO,OAAO,SAAS,CAAC,kBAAkB;QAC5C,KAAK;YACH,OAAO,OAAO,SAAS,CAAC,kBAAkB;QAC5C,KAAK;YACH,OAAO,OAAO,EAAE,CAAC,QAAQ;QAC3B;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 1917, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/personal%20projects/CREATEXP/src/hooks/useSortPersistence.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { SortCriterion } from '@/types/client';\n\nconst STORAGE_KEY = 'client-sort-criteria';\n\nexport function useSortPersistence() {\n  const [sortCriteria, setSortCriteria] = useState<SortCriterion[]>([]);\n\n  // Load from localStorage on mount\n  useEffect(() => {\n    try {\n      const saved = localStorage.getItem(STORAGE_KEY);\n      if (saved) {\n        const parsed = JSON.parse(saved);\n        setSortCriteria(parsed);\n      }\n    } catch (error) {\n      console.error('Failed to load sort criteria from localStorage:', error);\n    }\n  }, []);\n\n  // Save to localStorage whenever criteria changes\n  useEffect(() => {\n    try {\n      localStorage.setItem(STORAGE_KEY, JSON.stringify(sortCriteria));\n    } catch (error) {\n      console.error('Failed to save sort criteria to localStorage:', error);\n    }\n  }, [sortCriteria]);\n\n  return [sortCriteria, setSortCriteria] as const;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGA,MAAM,cAAc;AAEb,SAAS;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAEpE,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,OAAO;gBACT,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,gBAAgB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mDAAmD;QACnE;IACF,GAAG,EAAE;IAEL,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;YACF,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;QACnD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iDAAiD;QACjE;IACF,GAAG;QAAC;KAAa;IAEjB,OAAO;QAAC;QAAc;KAAgB;AACxC", "debugId": null}}, {"offset": {"line": 1956, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/personal%20projects/CREATEXP/src/hooks/useDebounce.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\n\nexport function useDebounce<T>(value: T, delay: number): T {\n  const [debouncedValue, setDebouncedValue] = useState<T>(value);\n\n  useEffect(() => {\n    const handler = setTimeout(() => {\n      setDebouncedValue(value);\n    }, delay);\n\n    return () => {\n      clearTimeout(handler);\n    };\n  }, [value, delay]);\n\n  return debouncedValue;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,SAAS,YAAe,KAAQ,EAAE,KAAa;IACpD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAK;IAExD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU,WAAW;YACzB,kBAAkB;QACpB,GAAG;QAEH,OAAO;YACL,aAAa;QACf;IACF,GAAG;QAAC;QAAO;KAAM;IAEjB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1980, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/personal%20projects/CREATEXP/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useMemo } from 'react';\nimport { ClientTable } from '@/components/ClientTable';\nimport { SortPanel } from '@/components/SortPanel';\nimport { mockClients } from '@/data/mockClients';\nimport { ClientFilter, SortCriterion, DateFilters } from '@/types/client';\nimport { multiSort } from '@/utils/sorting';\nimport { useSortPersistence } from '@/hooks/useSortPersistence';\nimport { useDebounce } from '@/hooks/useDebounce';\n\nexport default function Home() {\n  const [filter, setFilter] = useState<ClientFilter>('All');\n  const [sortCriteria, setSortCriteria] = useSortPersistence();\n  const [showSortPanel, setShowSortPanel] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [dateFilters, setDateFilters] = useState<DateFilters>({\n    createdAt: { from: undefined, to: undefined },\n    updatedAt: { from: undefined, to: undefined }\n  });\n\n  // Debounce search query to avoid excessive filtering\n  const debouncedSearchQuery = useDebounce(searchQuery, 300);\n\n  // Apply sorting to clients\n  const sortedClients = useMemo(() => {\n    return multiSort(mockClients, sortCriteria);\n  }, [sortCriteria]);\n\n  const handleApplySort = () => {\n    // Sort is applied automatically through useMemo\n  };\n\n  const handleClearAll = () => {\n    setSortCriteria([]);\n  };\n\n  const toggleSortPanel = () => {\n    setShowSortPanel(!showSortPanel);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"flex\">\n        {/* Main Content */}\n        <div className={`flex-1 p-6 transition-all duration-300 ${showSortPanel ? 'mr-80' : ''}`}>\n          <ClientTable\n            clients={sortedClients}\n            filter={filter}\n            onFilterChange={setFilter}\n            onToggleSort={toggleSortPanel}\n            sortCriteriaCount={sortCriteria.length}\n            searchQuery={searchQuery}\n            debouncedSearchQuery={debouncedSearchQuery}\n            onSearchChange={setSearchQuery}\n            dateFilters={dateFilters}\n            onDateFiltersChange={setDateFilters}\n          />\n\n        </div>\n\n        {/* Sort Panel */}\n        {showSortPanel && (\n          <div className=\"fixed right-0 top-0 h-full z-10 animate-in slide-in-from-right duration-300\">\n            <SortPanel\n              criteria={sortCriteria}\n              onCriteriaChange={setSortCriteria}\n              onApplySort={handleApplySort}\n              onClearAll={handleClearAll}\n              onClose={toggleSortPanel}\n            />\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AATA;;;;;;;;;AAWe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,kIAAA,CAAA,qBAAkB,AAAD;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;QAC1D,WAAW;YAAE,MAAM;YAAW,IAAI;QAAU;QAC5C,WAAW;YAAE,MAAM;YAAW,IAAI;QAAU;IAC9C;IAEA,qDAAqD;IACrD,MAAM,uBAAuB,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD,EAAE,aAAa;IAEtD,2BAA2B;IAC3B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,OAAO,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE,0HAAA,CAAA,cAAW,EAAE;IAChC,GAAG;QAAC;KAAa;IAEjB,MAAM,kBAAkB;IACtB,gDAAgD;IAClD;IAEA,MAAM,iBAAiB;QACrB,gBAAgB,EAAE;IACpB;IAEA,MAAM,kBAAkB;QACtB,iBAAiB,CAAC;IACpB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAW,CAAC,uCAAuC,EAAE,gBAAgB,UAAU,IAAI;8BACtF,cAAA,8OAAC,iIAAA,CAAA,cAAW;wBACV,SAAS;wBACT,QAAQ;wBACR,gBAAgB;wBAChB,cAAc;wBACd,mBAAmB,aAAa,MAAM;wBACtC,aAAa;wBACb,sBAAsB;wBACtB,gBAAgB;wBAChB,aAAa;wBACb,qBAAqB;;;;;;;;;;;gBAMxB,+BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,+HAAA,CAAA,YAAS;wBACR,UAAU;wBACV,kBAAkB;wBAClB,aAAa;wBACb,YAAY;wBACZ,SAAS;;;;;;;;;;;;;;;;;;;;;;AAOvB", "debugId": null}}]}