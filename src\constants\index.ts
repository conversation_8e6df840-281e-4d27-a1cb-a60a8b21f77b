// Application constants

export const APP_CONFIG = {
  name: 'CreatExp',
  description: 'A modern full-stack application',
  version: '1.0.0',
} as const;

export const API_ENDPOINTS = {
  auth: {
    login: '/api/auth/login',
    logout: '/api/auth/logout',
    register: '/api/auth/register',
    profile: '/api/auth/profile',
  },
  users: '/api/users',
} as const;

export const ROUTES = {
  home: '/',
  login: '/login',
  register: '/register',
  dashboard: '/dashboard',
  profile: '/profile',
} as const;

export const PAGINATION = {
  defaultLimit: 10,
  maxLimit: 100,
} as const;

export const VALIDATION = {
  email: {
    minLength: 5,
    maxLength: 255,
  },
  password: {
    minLength: 8,
    maxLength: 128,
  },
  name: {
    minLength: 2,
    maxLength: 50,
  },
} as const;

export const STORAGE_KEYS = {
  authToken: 'auth_token',
  user: 'user_data',
  theme: 'theme_preference',
} as const;
