{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/personal%20projects/CREATEXP/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/personal%20projects/CREATEXP/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAM6C;QAN7C,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD,GAN7C;IAOb,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/personal%20projects/CREATEXP/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/personal%20projects/CREATEXP/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACb,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,KAGY;QAHZ,EACpB,SAAS,EACT,GAAG,OAC6B,GAHZ;IAIpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 292, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/personal%20projects/CREATEXP/src/components/ClientTable.tsx"], "sourcesContent": ["'use client';\n\nimport { Client, ClientFilter } from '@/types/client';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from '@/components/ui/table';\nimport { Search, Filter, Plus } from 'lucide-react';\n\ninterface ClientTableProps {\n  clients: Client[];\n  filter: ClientFilter;\n  onFilterChange: (filter: ClientFilter) => void;\n  onToggleSort?: () => void;\n  sortCriteriaCount?: number;\n}\n\nexport function ClientTable({ clients, filter, onFilterChange, onToggleSort, sortCriteriaCount = 0 }: ClientTableProps) {\n  const formatDate = (date: Date) => {\n    return date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n    });\n  };\n\n  const getStatusBadgeVariant = (status: string) => {\n    return status === 'Active' ? 'default' : 'secondary';\n  };\n\n  const filteredClients = clients.filter(client => {\n    if (filter === 'All') return true;\n    return client.clientType === filter;\n  });\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h1 className=\"text-2xl font-semibold\">Clients</h1>\n        <Button className=\"bg-black text-white hover:bg-gray-800\">\n          <Plus className=\"w-4 h-4 mr-2\" />\n          Add Client\n        </Button>\n      </div>\n\n      {/* Filters */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"flex items-center space-x-2\">\n            {(['All', 'Individual', 'Company'] as ClientFilter[]).map((filterOption) => (\n              <button\n                key={filterOption}\n                onClick={() => onFilterChange(filterOption)}\n                className={`px-3 py-1 text-sm rounded-md transition-colors ${\n                  filter === filterOption\n                    ? 'bg-gray-100 text-gray-900 border-b-2 border-gray-900'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                {filterOption}\n              </button>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"relative\">\n            <Search className=\"w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search...\"\n              className=\"pl-10 pr-4 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            />\n          </div>\n          <Button variant=\"outline\" size=\"sm\" onClick={onToggleSort}>\n            <Filter className=\"w-4 h-4 mr-2\" />\n            {sortCriteriaCount}\n          </Button>\n        </div>\n      </div>\n\n      {/* Table */}\n      <div className=\"border rounded-lg\">\n        <Table>\n          <TableHeader>\n            <TableRow>\n              <TableHead className=\"w-12\">\n                <input type=\"checkbox\" className=\"rounded\" />\n              </TableHead>\n              <TableHead>Client ID</TableHead>\n              <TableHead>Client Name</TableHead>\n              <TableHead>Client Type</TableHead>\n              <TableHead>Email</TableHead>\n              <TableHead>Status</TableHead>\n              <TableHead>Updated By</TableHead>\n            </TableRow>\n          </TableHeader>\n          <TableBody>\n            {filteredClients.map((client) => (\n              <TableRow key={client.id} className=\"hover:bg-gray-50 transition-colors\">\n                <TableCell>\n                  <input type=\"checkbox\" className=\"rounded\" />\n                </TableCell>\n                <TableCell className=\"text-blue-600 font-medium\">\n                  {client.id}\n                </TableCell>\n                <TableCell className=\"font-medium\">\n                  {client.clientName}\n                </TableCell>\n                <TableCell>\n                  {client.clientType}\n                </TableCell>\n                <TableCell className=\"text-gray-600\">\n                  {client.email}\n                </TableCell>\n                <TableCell>\n                  <Badge variant={getStatusBadgeVariant(client.status)}>\n                    {client.status}\n                  </Badge>\n                </TableCell>\n                <TableCell className=\"text-gray-600\">\n                  hello world\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAQA;AAAA;AAAA;AAbA;;;;;;AAuBO,SAAS,YAAY,KAA0F;QAA1F,EAAE,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,YAAY,EAAE,oBAAoB,CAAC,EAAoB,GAA1F;IAC1B,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAO,WAAW,WAAW,YAAY;IAC3C;IAEA,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA;QACrC,IAAI,WAAW,OAAO,OAAO;QAC7B,OAAO,OAAO,UAAU,KAAK;IAC/B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyB;;;;;;kCACvC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,WAAU;;0CAChB,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,AAAC;gCAAC;gCAAO;gCAAc;6BAAU,CAAoB,GAAG,CAAC,CAAC,6BACzD,6LAAC;oCAEC,SAAS,IAAM,eAAe;oCAC9B,WAAW,AAAC,kDAIX,OAHC,WAAW,eACP,yDACA;8CAGL;mCARI;;;;;;;;;;;;;;;kCAcb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;0CAGd,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,SAAS;;kDAC3C,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCACjB;;;;;;;;;;;;;;;;;;;0BAMP,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;sCACJ,6LAAC,oIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;kDACP,6LAAC,oIAAA,CAAA,YAAS;wCAAC,WAAU;kDACnB,cAAA,6LAAC;4CAAM,MAAK;4CAAW,WAAU;;;;;;;;;;;kDAEnC,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;;;;;;;sCAGf,6LAAC,oIAAA,CAAA,YAAS;sCACP,gBAAgB,GAAG,CAAC,CAAC,uBACpB,6LAAC,oIAAA,CAAA,WAAQ;oCAAiB,WAAU;;sDAClC,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC;gDAAM,MAAK;gDAAW,WAAU;;;;;;;;;;;sDAEnC,6LAAC,oIAAA,CAAA,YAAS;4CAAC,WAAU;sDAClB,OAAO,EAAE;;;;;;sDAEZ,6LAAC,oIAAA,CAAA,YAAS;4CAAC,WAAU;sDAClB,OAAO,UAAU;;;;;;sDAEpB,6LAAC,oIAAA,CAAA,YAAS;sDACP,OAAO,UAAU;;;;;;sDAEpB,6LAAC,oIAAA,CAAA,YAAS;4CAAC,WAAU;sDAClB,OAAO,KAAK;;;;;;sDAEf,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAS,sBAAsB,OAAO,MAAM;0DAChD,OAAO,MAAM;;;;;;;;;;;sDAGlB,6LAAC,oIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAgB;;;;;;;mCArBxB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BtC;KAlHgB", "debugId": null}}, {"offset": {"line": 637, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/personal%20projects/CREATEXP/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,KAEoC;QAFpC,EACpB,GAAG,OACqD,GAFpC;IAGpB,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,KAEgC;QAFhC,EAC1B,GAAG,OACuD,GAFhC;IAG1B,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,KAEgC;QAFhC,EAC3B,GAAG,OACwD,GAFhC;IAG3B,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,KAIgC;QAJhC,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD,GAJhC;IAK3B,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,KAEgC;QAFhC,EACzB,GAAG,OACsD,GAFhC;IAGzB,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,KAQzB;QARyB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ,GARyB;IASxB,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,KAKgC;QALhC,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D,GALhC;IAMhC,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,KAEgC;QAFhC,EAC9B,GAAG,OAC2D,GAFhC;IAG9B,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,KAIgC;QAJhC,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D,GAJhC;IAK7B,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,KAM1B;QAN0B,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ,GAN0B;IAOzB,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,KAGgC;QAHhC,EAC7B,SAAS,EACT,GAAG,OAC0D,GAHhC;IAI7B,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,KAGC;QAHD,EAC5B,SAAS,EACT,GAAG,OAC0B,GAHD;IAI5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,KAEgC;QAFhC,EACvB,GAAG,OACoD,GAFhC;IAGvB,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,KAO/B;QAP+B,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ,GAP+B;IAQ9B,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 948, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/personal%20projects/CREATEXP/src/components/SortPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport {\n  DndContext,\n  closestCenter,\n  KeyboardSensor,\n  PointerSensor,\n  useSensor,\n  useSensors,\n  DragEndEvent,\n} from '@dnd-kit/core';\nimport {\n  arrayMove,\n  SortableContext,\n  sortableKeyboardCoordinates,\n  verticalListSortingStrategy,\n} from '@dnd-kit/sortable';\nimport {\n  useSortable,\n} from '@dnd-kit/sortable';\nimport { CSS } from '@dnd-kit/utilities';\nimport { SortCriterion, SortField, SortDirection } from '@/types/client';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport { \n  GripVertical, \n  ChevronUp, \n  ChevronDown, \n  X, \n  Plus,\n  ArrowUpDown,\n  Calendar,\n  User,\n  Hash\n} from 'lucide-react';\n\ninterface SortPanelProps {\n  criteria: SortCriterion[];\n  onCriteriaChange: (criteria: SortCriterion[]) => void;\n  onApplySort: () => void;\n  onClearAll: () => void;\n  onClose?: () => void;\n}\n\nconst sortFieldOptions: { value: SortField; label: string; icon: React.ReactNode }[] = [\n  { value: 'clientName', label: 'Client Name', icon: <User className=\"w-4 h-4\" /> },\n  { value: 'createdAt', label: 'Created At', icon: <Calendar className=\"w-4 h-4\" /> },\n  { value: 'updatedAt', label: 'Updated At', icon: <Calendar className=\"w-4 h-4\" /> },\n  { value: 'clientId', label: 'Client ID', icon: <Hash className=\"w-4 h-4\" /> },\n];\n\ninterface SortableItemProps {\n  criterion: SortCriterion;\n  onDirectionToggle: (id: string) => void;\n  onRemove: (id: string) => void;\n}\n\nfunction SortableItem({ criterion, onDirectionToggle, onRemove }: SortableItemProps) {\n  const {\n    attributes,\n    listeners,\n    setNodeRef,\n    transform,\n    transition,\n    isDragging,\n  } = useSortable({ id: criterion.id });\n\n  const style = {\n    transform: CSS.Transform.toString(transform),\n    transition,\n  };\n\n  const fieldOption = sortFieldOptions.find(option => option.value === criterion.field);\n\n  return (\n    <div\n      ref={setNodeRef}\n      style={style}\n      className={`flex items-center justify-between p-3 bg-white border rounded-lg transition-all duration-200 ${\n        isDragging ? 'shadow-lg opacity-50 scale-105' : 'shadow-sm hover:shadow-md'\n      }`}\n    >\n      <div className=\"flex items-center space-x-3\">\n        <button\n          className=\"cursor-grab active:cursor-grabbing text-gray-400 hover:text-gray-600\"\n          {...attributes}\n          {...listeners}\n        >\n          <GripVertical className=\"w-4 h-4\" />\n        </button>\n        \n        <div className=\"flex items-center space-x-2\">\n          {fieldOption?.icon}\n          <span className=\"font-medium\">{criterion.label}</span>\n        </div>\n      </div>\n\n      <div className=\"flex items-center space-x-2\">\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => onDirectionToggle(criterion.id)}\n          className=\"flex items-center space-x-1\"\n        >\n          {criterion.direction === 'asc' ? (\n            <>\n              <ChevronUp className=\"w-4 h-4\" />\n              <span>A-Z</span>\n            </>\n          ) : (\n            <>\n              <ChevronDown className=\"w-4 h-4\" />\n              <span>Z-A</span>\n            </>\n          )}\n        </Button>\n        \n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={() => onRemove(criterion.id)}\n          className=\"text-gray-400 hover:text-red-500\"\n        >\n          <X className=\"w-4 h-4\" />\n        </Button>\n      </div>\n    </div>\n  );\n}\n\nexport function SortPanel({ criteria, onCriteriaChange, onApplySort, onClearAll, onClose }: SortPanelProps) {\n  const sensors = useSensors(\n    useSensor(PointerSensor),\n    useSensor(KeyboardSensor, {\n      coordinateGetter: sortableKeyboardCoordinates,\n    })\n  );\n\n  const handleDragEnd = (event: DragEndEvent) => {\n    const { active, over } = event;\n\n    if (active.id !== over?.id) {\n      const oldIndex = criteria.findIndex(item => item.id === active.id);\n      const newIndex = criteria.findIndex(item => item.id === over?.id);\n      \n      onCriteriaChange(arrayMove(criteria, oldIndex, newIndex));\n    }\n  };\n\n  const handleDirectionToggle = (id: string) => {\n    const updatedCriteria = criteria.map(criterion =>\n      criterion.id === id\n        ? { ...criterion, direction: criterion.direction === 'asc' ? 'desc' : 'asc' as SortDirection }\n        : criterion\n    );\n    onCriteriaChange(updatedCriteria);\n  };\n\n  const handleRemove = (id: string) => {\n    onCriteriaChange(criteria.filter(criterion => criterion.id !== id));\n  };\n\n  const handleAddCriterion = (field: SortField) => {\n    const fieldOption = sortFieldOptions.find(option => option.value === field);\n    if (!fieldOption) return;\n\n    // Check if this field is already being sorted\n    if (criteria.some(criterion => criterion.field === field)) return;\n\n    const newCriterion: SortCriterion = {\n      id: `${field}-${Date.now()}`,\n      field,\n      direction: 'asc',\n      label: fieldOption.label,\n    };\n\n    onCriteriaChange([...criteria, newCriterion]);\n  };\n\n  const availableFields = sortFieldOptions.filter(\n    option => !criteria.some(criterion => criterion.field === option.value)\n  );\n\n  return (\n    <div className=\"w-80 bg-gray-50 border-l p-4 space-y-4\">\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-2\">\n          <ArrowUpDown className=\"w-5 h-5\" />\n          <h3 className=\"font-semibold\">Sort By</h3>\n        </div>\n        {onClose && (\n          <Button variant=\"ghost\" size=\"sm\" onClick={onClose}>\n            <X className=\"w-4 h-4\" />\n          </Button>\n        )}\n      </div>\n\n      <DndContext\n        sensors={sensors}\n        collisionDetection={closestCenter}\n        onDragEnd={handleDragEnd}\n      >\n        <SortableContext items={criteria.map(c => c.id)} strategy={verticalListSortingStrategy}>\n          <div className=\"space-y-2\">\n            {criteria.map((criterion) => (\n              <SortableItem\n                key={criterion.id}\n                criterion={criterion}\n                onDirectionToggle={handleDirectionToggle}\n                onRemove={handleRemove}\n              />\n            ))}\n          </div>\n        </SortableContext>\n      </DndContext>\n\n      {availableFields.length > 0 && (\n        <DropdownMenu>\n          <DropdownMenuTrigger asChild>\n            <Button variant=\"outline\" className=\"w-full\">\n              <Plus className=\"w-4 h-4 mr-2\" />\n              Add Sort Criterion\n            </Button>\n          </DropdownMenuTrigger>\n          <DropdownMenuContent className=\"w-56\">\n            {availableFields.map((field) => (\n              <DropdownMenuItem\n                key={field.value}\n                onClick={() => handleAddCriterion(field.value)}\n                className=\"flex items-center space-x-2\"\n              >\n                {field.icon}\n                <span>{field.label}</span>\n              </DropdownMenuItem>\n            ))}\n          </DropdownMenuContent>\n        </DropdownMenu>\n      )}\n\n      <div className=\"flex space-x-2 pt-4 border-t\">\n        <Button onClick={onClearAll} variant=\"outline\" className=\"flex-1\">\n          Clear all\n        </Button>\n        <Button onClick={onApplySort} className=\"flex-1 bg-black text-white hover:bg-gray-800\">\n          Apply Sort\n        </Button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AASA;AASA;AAEA;AAEA;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AA/BA;;;;;;;;AAmDA,MAAM,mBAAiF;IACrF;QAAE,OAAO;QAAc,OAAO;QAAe,oBAAM,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IAAa;IAChF;QAAE,OAAO;QAAa,OAAO;QAAc,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAAa;IAClF;QAAE,OAAO;QAAa,OAAO;QAAc,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAAa;IAClF;QAAE,OAAO;QAAY,OAAO;QAAa,oBAAM,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IAAa;CAC7E;AAQD,SAAS,aAAa,KAA6D;QAA7D,EAAE,SAAS,EAAE,iBAAiB,EAAE,QAAQ,EAAqB,GAA7D;;IACpB,MAAM,EACJ,UAAU,EACV,SAAS,EACT,UAAU,EACV,SAAS,EACT,UAAU,EACV,UAAU,EACX,GAAG,CAAA,GAAA,sKAAA,CAAA,cAAW,AAAD,EAAE;QAAE,IAAI,UAAU,EAAE;IAAC;IAEnC,MAAM,QAAQ;QACZ,WAAW,wKAAA,CAAA,MAAG,CAAC,SAAS,CAAC,QAAQ,CAAC;QAClC;IACF;IAEA,MAAM,cAAc,iBAAiB,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK,UAAU,KAAK;IAEpF,qBACE,6LAAC;QACC,KAAK;QACL,OAAO;QACP,WAAW,AAAC,gGAEX,OADC,aAAa,mCAAmC;;0BAGlD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,WAAU;wBACT,GAAG,UAAU;wBACb,GAAG,SAAS;kCAEb,cAAA,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;kCAG1B,6LAAC;wBAAI,WAAU;;4BACZ,wBAAA,kCAAA,YAAa,IAAI;0CAClB,6LAAC;gCAAK,WAAU;0CAAe,UAAU,KAAK;;;;;;;;;;;;;;;;;;0BAIlD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,kBAAkB,UAAU,EAAE;wBAC7C,WAAU;kCAET,UAAU,SAAS,KAAK,sBACvB;;8CACE,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,6LAAC;8CAAK;;;;;;;yDAGR;;8CACE,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC;8CAAK;;;;;;;;;;;;;kCAKZ,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,SAAS,UAAU,EAAE;wBACpC,WAAU;kCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKvB;GAvES;;QAQH,sKAAA,CAAA,cAAW;;;KARR;AAyEF,SAAS,UAAU,KAAgF;QAAhF,EAAE,QAAQ,EAAE,gBAAgB,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAkB,GAAhF;;IACxB,MAAM,UAAU,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EACvB,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EAAE,8JAAA,CAAA,gBAAa,GACvB,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EAAE,8JAAA,CAAA,iBAAc,EAAE;QACxB,kBAAkB,sKAAA,CAAA,8BAA2B;IAC/C;IAGF,MAAM,gBAAgB,CAAC;QACrB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QAEzB,IAAI,OAAO,EAAE,MAAK,iBAAA,2BAAA,KAAM,EAAE,GAAE;YAC1B,MAAM,WAAW,SAAS,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,EAAE;YACjE,MAAM,WAAW,SAAS,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,MAAK,iBAAA,2BAAA,KAAM,EAAE;YAEhE,iBAAiB,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,UAAU,UAAU;QACjD;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,MAAM,kBAAkB,SAAS,GAAG,CAAC,CAAA,YACnC,UAAU,EAAE,KAAK,KACb;gBAAE,GAAG,SAAS;gBAAE,WAAW,UAAU,SAAS,KAAK,QAAQ,SAAS;YAAuB,IAC3F;QAEN,iBAAiB;IACnB;IAEA,MAAM,eAAe,CAAC;QACpB,iBAAiB,SAAS,MAAM,CAAC,CAAA,YAAa,UAAU,EAAE,KAAK;IACjE;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,cAAc,iBAAiB,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK;QACrE,IAAI,CAAC,aAAa;QAElB,8CAA8C;QAC9C,IAAI,SAAS,IAAI,CAAC,CAAA,YAAa,UAAU,KAAK,KAAK,QAAQ;QAE3D,MAAM,eAA8B;YAClC,IAAI,AAAC,GAAW,OAAT,OAAM,KAAc,OAAX,KAAK,GAAG;YACxB;YACA,WAAW;YACX,OAAO,YAAY,KAAK;QAC1B;QAEA,iBAAiB;eAAI;YAAU;SAAa;IAC9C;IAEA,MAAM,kBAAkB,iBAAiB,MAAM,CAC7C,CAAA,SAAU,CAAC,SAAS,IAAI,CAAC,CAAA,YAAa,UAAU,KAAK,KAAK,OAAO,KAAK;IAGxE,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC;gCAAG,WAAU;0CAAgB;;;;;;;;;;;;oBAE/B,yBACC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAK,SAAS;kCACzC,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKnB,6LAAC,8JAAA,CAAA,aAAU;gBACT,SAAS;gBACT,oBAAoB,8JAAA,CAAA,gBAAa;gBACjC,WAAW;0BAEX,cAAA,6LAAC,sKAAA,CAAA,kBAAe;oBAAC,OAAO,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;oBAAG,UAAU,sKAAA,CAAA,8BAA2B;8BACpF,cAAA,6LAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,0BACb,6LAAC;gCAEC,WAAW;gCACX,mBAAmB;gCACnB,UAAU;+BAHL,UAAU,EAAE;;;;;;;;;;;;;;;;;;;;YAU1B,gBAAgB,MAAM,GAAG,mBACxB,6LAAC,+IAAA,CAAA,eAAY;;kCACX,6LAAC,+IAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,WAAU;;8CAClC,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIrC,6LAAC,+IAAA,CAAA,sBAAmB;wBAAC,WAAU;kCAC5B,gBAAgB,GAAG,CAAC,CAAC,sBACpB,6LAAC,+IAAA,CAAA,mBAAgB;gCAEf,SAAS,IAAM,mBAAmB,MAAM,KAAK;gCAC7C,WAAU;;oCAET,MAAM,IAAI;kDACX,6LAAC;kDAAM,MAAM,KAAK;;;;;;;+BALb,MAAM,KAAK;;;;;;;;;;;;;;;;0BAY1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAY,SAAQ;wBAAU,WAAU;kCAAS;;;;;;kCAGlE,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAa,WAAU;kCAA+C;;;;;;;;;;;;;;;;;;AAM/F;IAvHgB;;QACE,8JAAA,CAAA,aAAU;;;MADZ", "debugId": null}}, {"offset": {"line": 1402, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/personal%20projects/CREATEXP/src/data/mockClients.ts"], "sourcesContent": ["import { Client } from '@/types/client';\n\n// Mock client data\nexport const mockClients: Client[] = [\n  {\n    id: 20,\n    clientName: '<PERSON>',\n    clientType: 'Individual',\n    email: 'johndo<PERSON>@email.com',\n    createdAt: new Date('2024-01-15T10:30:00Z'),\n    updatedAt: new Date('2024-07-20T14:45:00Z'),\n    status: 'Active'\n  },\n  {\n    id: 21,\n    clientName: 'Test Test',\n    clientType: 'Individual',\n    email: '<EMAIL>',\n    createdAt: new Date('2024-02-10T09:15:00Z'),\n    updatedAt: new Date('2024-07-25T16:20:00Z'),\n    status: 'Active'\n  },\n  {\n    id: 22,\n    clientName: 'Acme Corporation',\n    clientType: 'Company',\n    email: '<EMAIL>',\n    createdAt: new Date('2024-01-05T08:00:00Z'),\n    updatedAt: new Date('2024-07-28T11:30:00Z'),\n    status: 'Active'\n  },\n  {\n    id: 23,\n    clientName: '<PERSON>',\n    clientType: 'Individual',\n    email: '<EMAIL>',\n    createdAt: new Date('2024-03-20T13:45:00Z'),\n    updatedAt: new Date('2024-07-22T10:15:00Z'),\n    status: 'Inactive'\n  },\n  {\n    id: 24,\n    clientName: 'Tech Solutions Inc',\n    clientType: 'Company',\n    email: '<EMAIL>',\n    createdAt: new Date('2024-01-30T15:20:00Z'),\n    updatedAt: new Date('2024-07-26T09:45:00Z'),\n    status: 'Active'\n  },\n  {\n    id: 25,\n    clientName: 'Bob Johnson',\n    clientType: 'Individual',\n    email: '<EMAIL>',\n    createdAt: new Date('2024-04-12T11:10:00Z'),\n    updatedAt: new Date('2024-07-24T15:30:00Z'),\n    status: 'Active'\n  },\n  {\n    id: 26,\n    clientName: 'Global Enterprises',\n    clientType: 'Company',\n    email: '<EMAIL>',\n    createdAt: new Date('2024-02-28T16:40:00Z'),\n    updatedAt: new Date('2024-07-29T12:20:00Z'),\n    status: 'Active'\n  },\n  {\n    id: 27,\n    clientName: 'Alice Brown',\n    clientType: 'Individual',\n    email: '<EMAIL>',\n    createdAt: new Date('2024-05-08T14:25:00Z'),\n    updatedAt: new Date('2024-07-23T17:10:00Z'),\n    status: 'Inactive'\n  }\n];\n"], "names": [], "mappings": ";;;AAGO,MAAM,cAAwB;IACnC;QACE,IAAI;QACJ,YAAY;QACZ,YAAY;QACZ,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ;IACV;IACA;QACE,IAAI;QACJ,YAAY;QACZ,YAAY;QACZ,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ;IACV;IACA;QACE,IAAI;QACJ,YAAY;QACZ,YAAY;QACZ,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ;IACV;IACA;QACE,IAAI;QACJ,YAAY;QACZ,YAAY;QACZ,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ;IACV;IACA;QACE,IAAI;QACJ,YAAY;QACZ,YAAY;QACZ,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ;IACV;IACA;QACE,IAAI;QACJ,YAAY;QACZ,YAAY;QACZ,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ;IACV;IACA;QACE,IAAI;QACJ,YAAY;QACZ,YAAY;QACZ,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ;IACV;IACA;QACE,IAAI;QACJ,YAAY;QACZ,YAAY;QACZ,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ;IACV;CACD", "debugId": null}}, {"offset": {"line": 1488, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/personal%20projects/CREATEXP/src/utils/sorting.ts"], "sourcesContent": ["import { Client, SortCriterion } from '@/types/client';\n\n/**\n * Multi-sort function that applies multiple sort criteria in order of priority\n */\nexport function multiSort(clients: Client[], criteria: SortCriterion[]): Client[] {\n  if (criteria.length === 0) {\n    return [...clients];\n  }\n\n  return [...clients].sort((a, b) => {\n    for (const criterion of criteria) {\n      let aValue: any;\n      let bValue: any;\n\n      // Get the values to compare based on the field\n      switch (criterion.field) {\n        case 'clientName':\n          aValue = a.clientName.toLowerCase();\n          bValue = b.clientName.toLowerCase();\n          break;\n        case 'createdAt':\n          aValue = a.createdAt.getTime();\n          bValue = b.createdAt.getTime();\n          break;\n        case 'updatedAt':\n          aValue = a.updatedAt.getTime();\n          bValue = b.updatedAt.getTime();\n          break;\n        case 'clientId':\n          aValue = a.id;\n          bValue = b.id;\n          break;\n        default:\n          continue;\n      }\n\n      // Compare the values\n      let comparison = 0;\n      \n      if (aValue < bValue) {\n        comparison = -1;\n      } else if (aValue > bValue) {\n        comparison = 1;\n      }\n\n      // If values are equal, continue to next criterion\n      if (comparison === 0) {\n        continue;\n      }\n\n      // Apply sort direction\n      return criterion.direction === 'asc' ? comparison : -comparison;\n    }\n\n    // If all criteria result in equality, maintain original order\n    return 0;\n  });\n}\n\n/**\n * Helper function to get display value for a sort field\n */\nexport function getSortDisplayValue(client: Client, field: string): string {\n  switch (field) {\n    case 'clientName':\n      return client.clientName;\n    case 'createdAt':\n      return client.createdAt.toLocaleDateString();\n    case 'updatedAt':\n      return client.updatedAt.toLocaleDateString();\n    case 'clientId':\n      return client.id.toString();\n    default:\n      return '';\n  }\n}\n"], "names": [], "mappings": ";;;;AAKO,SAAS,UAAU,OAAiB,EAAE,QAAyB;IACpE,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,OAAO;eAAI;SAAQ;IACrB;IAEA,OAAO;WAAI;KAAQ,CAAC,IAAI,CAAC,CAAC,GAAG;QAC3B,KAAK,MAAM,aAAa,SAAU;YAChC,IAAI;YACJ,IAAI;YAEJ,+CAA+C;YAC/C,OAAQ,UAAU,KAAK;gBACrB,KAAK;oBACH,SAAS,EAAE,UAAU,CAAC,WAAW;oBACjC,SAAS,EAAE,UAAU,CAAC,WAAW;oBACjC;gBACF,KAAK;oBACH,SAAS,EAAE,SAAS,CAAC,OAAO;oBAC5B,SAAS,EAAE,SAAS,CAAC,OAAO;oBAC5B;gBACF,KAAK;oBACH,SAAS,EAAE,SAAS,CAAC,OAAO;oBAC5B,SAAS,EAAE,SAAS,CAAC,OAAO;oBAC5B;gBACF,KAAK;oBACH,SAAS,EAAE,EAAE;oBACb,SAAS,EAAE,EAAE;oBACb;gBACF;oBACE;YACJ;YAEA,qBAAqB;YACrB,IAAI,aAAa;YAEjB,IAAI,SAAS,QAAQ;gBACnB,aAAa,CAAC;YAChB,OAAO,IAAI,SAAS,QAAQ;gBAC1B,aAAa;YACf;YAEA,kDAAkD;YAClD,IAAI,eAAe,GAAG;gBACpB;YACF;YAEA,uBAAuB;YACvB,OAAO,UAAU,SAAS,KAAK,QAAQ,aAAa,CAAC;QACvD;QAEA,8DAA8D;QAC9D,OAAO;IACT;AACF;AAKO,SAAS,oBAAoB,MAAc,EAAE,KAAa;IAC/D,OAAQ;QACN,KAAK;YACH,OAAO,OAAO,UAAU;QAC1B,KAAK;YACH,OAAO,OAAO,SAAS,CAAC,kBAAkB;QAC5C,KAAK;YACH,OAAO,OAAO,SAAS,CAAC,kBAAkB;QAC5C,KAAK;YACH,OAAO,OAAO,EAAE,CAAC,QAAQ;QAC3B;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 1566, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/personal%20projects/CREATEXP/src/hooks/useSortPersistence.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { SortCriterion } from '@/types/client';\n\nconst STORAGE_KEY = 'client-sort-criteria';\n\nexport function useSortPersistence() {\n  const [sortCriteria, setSortCriteria] = useState<SortCriterion[]>([]);\n\n  // Load from localStorage on mount\n  useEffect(() => {\n    try {\n      const saved = localStorage.getItem(STORAGE_KEY);\n      if (saved) {\n        const parsed = JSON.parse(saved);\n        setSortCriteria(parsed);\n      }\n    } catch (error) {\n      console.error('Failed to load sort criteria from localStorage:', error);\n    }\n  }, []);\n\n  // Save to localStorage whenever criteria changes\n  useEffect(() => {\n    try {\n      localStorage.setItem(STORAGE_KEY, JSON.stringify(sortCriteria));\n    } catch (error) {\n      console.error('Failed to save sort criteria to localStorage:', error);\n    }\n  }, [sortCriteria]);\n\n  return [sortCriteria, setSortCriteria] as const;\n}\n"], "names": [], "mappings": ";;;AAAA;;;AAGA,MAAM,cAAc;AAEb,SAAS;;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAEpE,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI;gBACF,MAAM,QAAQ,aAAa,OAAO,CAAC;gBACnC,IAAI,OAAO;oBACT,MAAM,SAAS,KAAK,KAAK,CAAC;oBAC1B,gBAAgB;gBAClB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mDAAmD;YACnE;QACF;uCAAG,EAAE;IAEL,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI;gBACF,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;YACnD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iDAAiD;YACjE;QACF;uCAAG;QAAC;KAAa;IAEjB,OAAO;QAAC;QAAc;KAAgB;AACxC;GA1BgB", "debugId": null}}, {"offset": {"line": 1617, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/personal%20projects/CREATEXP/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useMemo } from 'react';\nimport { ClientTable } from '@/components/ClientTable';\nimport { SortPanel } from '@/components/SortPanel';\nimport { DemoInstructions } from '@/components/DemoInstructions';\nimport { mockClients } from '@/data/mockClients';\nimport { ClientFilter, SortCriterion } from '@/types/client';\nimport { multiSort } from '@/utils/sorting';\nimport { useSortPersistence } from '@/hooks/useSortPersistence';\n\nexport default function Home() {\n  const [filter, setFilter] = useState<ClientFilter>('All');\n  const [sortCriteria, setSortCriteria] = useSortPersistence();\n  const [showSortPanel, setShowSortPanel] = useState(false);\n\n  // Apply sorting to clients\n  const sortedClients = useMemo(() => {\n    return multiSort(mockClients, sortCriteria);\n  }, [sortCriteria]);\n\n  const handleApplySort = () => {\n    // Sort is applied automatically through useMemo\n    // This could be used for additional actions like analytics\n    console.log('Sort applied:', sortCriteria);\n  };\n\n  const handleClearAll = () => {\n    setSortCriteria([]);\n  };\n\n  const toggleSortPanel = () => {\n    setShowSortPanel(!showSortPanel);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"flex\">\n        {/* Main Content */}\n        <div className={`flex-1 p-6 transition-all duration-300 ${showSortPanel ? 'mr-80' : ''}`}>\n          <ClientTable\n            clients={sortedClients}\n            filter={filter}\n            onFilterChange={setFilter}\n            onToggleSort={toggleSortPanel}\n            sortCriteriaCount={sortCriteria.length}\n          />\n\n        </div>\n\n        {/* Sort Panel */}\n        {showSortPanel && (\n          <div className=\"fixed right-0 top-0 h-full z-10 animate-in slide-in-from-right duration-300\">\n            <SortPanel\n              criteria={sortCriteria}\n              onCriteriaChange={setSortCriteria}\n              onApplySort={handleApplySort}\n              onClearAll={handleClearAll}\n              onClose={toggleSortPanel}\n            />\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAEA;AACA;;;AATA;;;;;;;AAWe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qIAAA,CAAA,qBAAkB,AAAD;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,2BAA2B;IAC3B,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;uCAAE;YAC5B,OAAO,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD,EAAE,6HAAA,CAAA,cAAW,EAAE;QAChC;sCAAG;QAAC;KAAa;IAEjB,MAAM,kBAAkB;QACtB,gDAAgD;QAChD,2DAA2D;QAC3D,QAAQ,GAAG,CAAC,iBAAiB;IAC/B;IAEA,MAAM,iBAAiB;QACrB,gBAAgB,EAAE;IACpB;IAEA,MAAM,kBAAkB;QACtB,iBAAiB,CAAC;IACpB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAW,AAAC,0CAAsE,OAA7B,gBAAgB,UAAU;8BAClF,cAAA,6LAAC,oIAAA,CAAA,cAAW;wBACV,SAAS;wBACT,QAAQ;wBACR,gBAAgB;wBAChB,cAAc;wBACd,mBAAmB,aAAa,MAAM;;;;;;;;;;;gBAMzC,+BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,kIAAA,CAAA,YAAS;wBACR,UAAU;wBACV,kBAAkB;wBAClB,aAAa;wBACb,YAAY;wBACZ,SAAS;;;;;;;;;;;;;;;;;;;;;;AAOvB;GAtDwB;;QAEkB,qIAAA,CAAA,qBAAkB;;;KAFpC", "debugId": null}}]}