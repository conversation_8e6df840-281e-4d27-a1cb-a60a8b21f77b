'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export function DemoInstructions() {
  return (
    <Card className="mb-6 bg-blue-50 border-blue-200">
      <CardHeader>
        <CardTitle className="text-blue-900">🎯 Demo Instructions</CardTitle>
        <CardDescription className="text-blue-700">
          Try out the multi-sort functionality with drag-and-drop!
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex flex-wrap gap-2">
          <Badge variant="outline" className="bg-white">
            1. Click the filter button (with number) to open sort panel
          </Badge>
          <Badge variant="outline" className="bg-white">
            2. Add sort criteria using "Add Sort Criterion"
          </Badge>
          <Badge variant="outline" className="bg-white">
            3. Drag criteria up/down to change priority
          </Badge>
          <Badge variant="outline" className="bg-white">
            4. Toggle A-Z / Z-A for each criterion
          </Badge>
          <Badge variant="outline" className="bg-white">
            5. Sort settings persist in localStorage
          </Badge>
        </div>
        <p className="text-sm text-blue-600">
          <strong>Features:</strong> Multi-sort with priority, drag-and-drop reordering, 
          client type filtering, localStorage persistence, smooth animations
        </p>
      </CardContent>
    </Card>
  );
}
